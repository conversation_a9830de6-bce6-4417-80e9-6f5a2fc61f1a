[1750767351.069405] [WARN]   Logger initialized successfully
[1750767351.070103] [INFO]   === Koneko Rust Implementation Starting ===
[1750767351.070326] [INFO]   !!! Return address finder test enabled via command-line flag !!!
[1750767351.070748] [INFO]   Initializing global variables
[1750767351.071230] [INFO]   Collecting call r12 gadgets
[1750767351.146600] [INFO]   Checking for sandbox/VM environment
[1750767356.190201] [INFO]   Sandbox/VM check passed
[1750767356.190703] [INFO]   Running return address finder test
[1750767356.190826] [INFO]   Testing return address finder
[1750767356.191045] [INFO]   BaseThreadInitThunk found at address: 0x7ffa3e3a4ec0
[1750767356.260700] [INFO]   Found BaseThreadInitThunk return address at 0x7ffa3e3a4efe (offset: 62)
[1750767356.261213] [INFO]   Hardcoded BaseThreadInitThunk return address would be: 0x7ffa3e3a4ed7 (offset: 0x17)
[1750767356.261699] [INFO]   ⚠️ Dynamic return address differs from hardcoded offset: Dynamic: 0x7ffa3e3a4efe, Hardcoded: 0x7ffa3e3a4ed7
[1750767356.262094] [INFO]   RtlUserThreadStart found at address: 0x7ffa403ae1e0
[1750767356.262616] [INFO]   Found RtlUserThreadStart return address at 0x7ffa403ae23b (offset: 91)
[1750767356.263041] [INFO]   Hardcoded RtlUserThreadStart return address would be: 0x7ffa403ae20c (offset: 0x2c)
[1750767356.263338] [INFO]   ⚠️ Dynamic return address differs from hardcoded offset: Dynamic: 0x7ffa403ae23b, Hardcoded: 0x7ffa403ae20c
[1750767356.263636] [INFO]   Sleep found at address: 0x7ffa3e3aae30
[1750767356.264463] [INFO]   Found Sleep return address at 0x7ffa3e3aaede (offset: 174)
[1750767356.264715] [INFO]   SleepEx found at address: 0x7ffa3e3b38f0
[1750767356.266282] [INFO]   Found SleepEx return address at 0x7ffa3e3b47b5 (offset: 3781)
[1750767356.266652] [INFO]   VirtualProtect found at address: 0x7ffa3e3ab990
[1750767356.267412] [INFO]   Found VirtualProtect return address at 0x7ffa3e3aba93 (offset: 259)
[1750767356.267717] [INFO]   NtQueryVirtualMemory found at address: 0x7ffa403cef60
[1750767356.268206] [INFO]   Found NtQueryVirtualMemory return address at 0x7ffa403cef77 (offset: 23)
[1750767356.268504] [INFO]   Return address finder test complete
[1750767356.268806] [INFO]   Return address finder test completed
[1750767356.268946] [INFO]   Starting main functionality
[1750767356.269077] [INFO]   Entering run_me() function
[1750767356.269306] [INFO]   Performing KUSER_SHARED_DATA checks
[1750767356.269533] [INFO]   KUSER_SHARED_DATA checks passed
[1750767356.269849] [INFO]   Checking for VDLL / Defender emulator
[1750767356.270069] [INFO]   VDLL / Defender emulator check passed
[1750767356.270255] [INFO]   Checking for debugger using NtQueryInformationProcess
[1750767356.351383] [INFO]   Debugger check passed
[1750767356.351869] [INFO]   Starting shellcode deobfuscation and preparation
[1750767356.351960] [INFO]   Deobfuscating shellcode using the original Koneko approach
[1750767356.352139] [INFO]   Shellcode deobfuscation complete: 392 bytes
[1750767356.352449] [INFO]   Allocating memory for shellcode
[1750767356.441832] [INFO]   Using NtAllocateVirtualMemory for shellcode allocation
[1750767356.442522] [INFO]   Verifying memory protection flags after allocation for 100% fidelity with original Koneko C++ implementation
[1750767356.522519] [INFO]   Writing shellcode to allocated memory
[1750767356.522973] [INFO]   Using WriteProcessMemory for direct byte-for-byte copying of shellcode
[1750767356.523259] [INFO]   Writing shellcode with WriteProcessMemory for 100% fidelity
[1750767356.523523] [INFO]   Verifying written shellcode
[1750767356.523875] [INFO]   ✅ Shellcode written correctly
[1750767356.524242] [INFO]   Changing memory protection to PAGE_EXECUTE_READWRITE after writing shellcode
[1750767356.608604] [INFO]   Shellcode successfully written to executable memory
[1750767356.609032] [INFO]   Hooking Sleep functions
[1750767356.860690] [INFO]   Sleep functions hooked successfully
[1750767356.861170] [INFO]   Converting thread to fiber
[1750767356.927623] [INFO]   MEMORY PROTECTION for Stack memory before fiber creation at address 0x0:
[1750767356.928120] [INFO]     Base address: 0x0
[1750767356.928278] [INFO]     Allocation base: 0x0
[1750767356.928450] [INFO]     Allocation protection: 0x0
[1750767356.928617] [INFO]     Region size: 2147352576 bytes
[1750767356.929079] [INFO]     Current protection: 0x1
[1750767356.929207] [INFO]     Memory state: 0x10000
[1750767356.929363] [INFO]     Memory type: 0x0
[1750767356.929615] [INFO]     Memory protection flags breakdown:
[1750767356.929802] [INFO]       PAGE_NOACCESS (0x01): Set
[1750767356.929983] [INFO]       PAGE_READONLY (0x02): Not set
[1750767356.930308] [INFO]       PAGE_READWRITE (0x04): Not set
[1750767356.930480] [INFO]       PAGE_WRITECOPY (0x08): Not set
[1750767356.931265] [INFO]       PAGE_EXECUTE (0x10): Not set
[1750767356.931671] [INFO]       PAGE_EXECUTE_READ (0x20): Not set
[1750767356.931856] [INFO]       PAGE_EXECUTE_READWRITE (0x40): Not set
[1750767356.932060] [INFO]       PAGE_EXECUTE_WRITECOPY (0x80): Not set
[1750767356.932286] [INFO]       PAGE_GUARD (0x100): Not set
[1750767356.932551] [INFO]       PAGE_NOCACHE (0x200): Not set
[1750767356.932903] [INFO]       PAGE_WRITECOMBINE (0x400): Not set
[1750767356.933079] [INFO]   STACK STATE BEFORE THREAD-TO-FIBER CONVERSION: RSP = 0x0000001E1DF39030
[1750767356.933463] [INFO]   REGISTER STATE BEFORE THREAD-TO-FIBER CONVERSION: RSP=0x0000001E1DF39000 RIP=0x00007FF6D0C2FDE8
[1750767356.933739] [INFO]   START OPERATION: ConvertThreadToFiber (timestamp: 1750767356.933737)
[1750767356.933984] [INFO]   MEMORY ADDRESS: MAIN_FIBER before creation at 0x0 (decimal: 0)
[1750767356.934274] [INFO]   FUNCTION CALL: ConvertThreadToFiber(Function: 0x7ff6d0c889b2, Args: 1, Gadget: 0x7ffa403c22fe, Parameter: 0x0)
[1750767356.934563] [INFO]   Using CallR12 to call ConvertThreadToFiber for 100% fidelity
[1750767356.934860] [INFO]   MEMORY ADDRESS: MAIN_FIBER after creation at 0x1273a69f400 (decimal: 1267995374592)
[1750767356.935105] [INFO]   END OPERATION: ConvertThreadToFiber (duration: 0.001362 seconds)
[1750767356.935324] [INFO]   STACK STATE AFTER THREAD-TO-FIBER CONVERSION: RSP = 0x0000001E1DF39030
[1750767356.935603] [INFO]   REGISTER STATE AFTER THREAD-TO-FIBER CONVERSION: RSP=0x0000001E1DF39000 RIP=0x00007FF6D0C2FDE8
[1750767356.935802] [INFO]   Thread successfully converted to fiber
[1750767356.936042] [INFO]   Creating shellcode fiber
[1750767356.936289] [INFO]   Using default stack size (0) for shellcode fiber as in original C++ implementation
[1750767356.936579] [INFO]   Dumping complete shellcode content for debugging and verification with original Koneko shellcode
[1750767356.936884] [INFO]   Successfully deobfuscated shellcode using original Koneko approach
[1750767356.937156] [INFO]   ✅ Shellcode was written correctly (all bytes match)
[1750767356.937444] [INFO]   ==================== FIBER CREATION START ====================
[1750767356.937737] [INFO]   ==================== SHELLCODE MEMORY DETAILS ====================
[1750767356.937967] [INFO]   MEMORY ADDRESS: SHELLCODE_ADDRESS at 0x1273a670000 (decimal: 1267995181056)
[1750767356.938208] [INFO]   Shellcode size: 392 bytes
[1750767357.026427] [INFO]   MEMORY PROTECTION for Shellcode memory at address 0x1273a670000:
[1750767357.027001] [INFO]     Base address: 0x1273a670000
[1750767357.027341] [INFO]     Allocation base: 0x1273a670000
[1750767357.027611] [INFO]     Allocation protection: 0x40
[1750767357.027998] [INFO]     Region size: 4096 bytes
[1750767357.028337] [INFO]     Current protection: 0x40
[1750767357.028635] [INFO]     Memory state: 0x1000
[1750767357.028965] [INFO]     Memory type: 0x20000
[1750767357.029186] [INFO]     Memory protection flags breakdown:
[1750767357.029445] [INFO]       PAGE_NOACCESS (0x01): Not set
[1750767357.029629] [INFO]       PAGE_READONLY (0x02): Not set
[1750767357.029864] [INFO]       PAGE_READWRITE (0x04): Not set
[1750767357.030086] [INFO]       PAGE_WRITECOPY (0x08): Not set
[1750767357.030380] [INFO]       PAGE_EXECUTE (0x10): Not set
[1750767357.030566] [INFO]       PAGE_EXECUTE_READ (0x20): Not set
[1750767357.030900] [INFO]       PAGE_EXECUTE_READWRITE (0x40): Set
[1750767357.031174] [INFO]       PAGE_EXECUTE_WRITECOPY (0x80): Not set
[1750767357.031446] [INFO]       PAGE_GUARD (0x100): Not set
[1750767357.031736] [INFO]       PAGE_NOCACHE (0x200): Not set
[1750767357.032075] [INFO]       PAGE_WRITECOMBINE (0x400): Not set
[1750767357.032291] [INFO]   First 64 bytes of shellcode:
[1750767357.032500] [INFO]     0000: 48 31 C9 48 81 E9 D4 FF FF FF 48 8D 05 EF FF FF  | H1.H......H.....
[1750767357.032824] [INFO]     0010: FF 48 BB 44 F6 A4 0B 5F 89 5D 7F 48 31 58 27 48  | .H.D..._.].H1X'H
[1750767357.033043] [INFO]     0020: 2D F8 FF FF FF E2 F4 B8 BE 27 EF AF 61 9D 7F 44  | -........'..a..D
[1750767357.033297] [INFO]     0030: F6 E5 5A 1E D9 0F 2E 12 BE 95 D9 3A C1 D6 2D 24  | ..Z........:..-$
[1750767357.033512] [INFO]   ==================== END SHELLCODE MEMORY DETAILS ====================
[1750767357.110970] [INFO]   MEMORY PROTECTION for Shellcode memory before fiber creation at address 0x1273a670000:
[1750767357.111474] [INFO]     Base address: 0x1273a670000
[1750767357.111867] [INFO]     Allocation base: 0x1273a670000
[1750767357.112188] [INFO]     Allocation protection: 0x40
[1750767357.112588] [INFO]     Region size: 4096 bytes
[1750767357.112997] [INFO]     Current protection: 0x40
[1750767357.113365] [INFO]     Memory state: 0x1000
[1750767357.113754] [INFO]     Memory type: 0x20000
[1750767357.114072] [INFO]     Memory protection flags breakdown:
[1750767357.114384] [INFO]       PAGE_NOACCESS (0x01): Not set
[1750767357.114597] [INFO]       PAGE_READONLY (0x02): Not set
[1750767357.114812] [INFO]       PAGE_READWRITE (0x04): Not set
[1750767357.115122] [INFO]       PAGE_WRITECOPY (0x08): Not set
[1750767357.115362] [INFO]       PAGE_EXECUTE (0x10): Not set
[1750767357.115542] [INFO]       PAGE_EXECUTE_READ (0x20): Not set
[1750767357.115760] [INFO]       PAGE_EXECUTE_READWRITE (0x40): Set
[1750767357.115978] [INFO]       PAGE_EXECUTE_WRITECOPY (0x80): Not set
[1750767357.116194] [INFO]       PAGE_GUARD (0x100): Not set
[1750767357.116381] [INFO]       PAGE_NOCACHE (0x200): Not set
[1750767357.116592] [INFO]       PAGE_WRITECOMBINE (0x400): Not set
[1750767357.116800] [INFO]   STACK STATE BEFORE SHELLCODE FIBER CREATION: RSP = 0x0000001E1DF39030
[1750767357.117098] [INFO]   REGISTER STATE BEFORE SHELLCODE FIBER CREATION: RSP=0x0000001E1DF39000 RIP=0x00007FF6D0C2FDE8
[1750767357.117408] [INFO]   START OPERATION: CreateFiber (timestamp: 1750767357.117403)
[1750767357.117761] [INFO]   MEMORY ADDRESS: SHELLCODE_FIBER before creation at 0x0 (decimal: 0)
[1750767357.117900] [INFO]   MEMORY ADDRESS: SHELLCODE_ADDRESS at 0x1273a670000 (decimal: 1267995181056)
[1750767357.183122] [INFO]   Using CallR12 to call CreateFiber with shellcode address as the function
[1750767357.183618] [INFO]   Using shellcode address directly as the fiber function for 100% fidelity with original C++ implementation
[1750767357.183807] [INFO]   Using default stack size (NULL/0) for shellcode fiber for 100% fidelity with original C++ implementation
[1750767357.184078] [INFO]   MEMORY ADDRESS: SHELLCODE_FIBER after creation at 0x1273a69cfe0 (decimal: 1267995365344)
[1750767357.184471] [INFO]   END OPERATION: CreateFiber (duration: 0.067066 seconds)
[1750767357.184740] [INFO]   STACK STATE AFTER SHELLCODE FIBER CREATION: RSP = 0x0000001E1DF39030
[1750767357.185013] [INFO]   REGISTER STATE AFTER SHELLCODE FIBER CREATION: RSP=0x0000001E1DF39000 RIP=0x00007FF6D0C2FDE8
[1750767357.185368] [INFO]   ==================== FIBER CREATION END ====================
[1750767357.185706] [INFO]   Shellcode fiber created successfully
[1750767357.186067] [INFO]   ==================== SHELLCODE MEMORY DETAILS ====================
[1750767357.186492] [INFO]   MEMORY ADDRESS: SHELLCODE_ADDRESS at 0x1273a670000 (decimal: 1267995181056)
[1750767357.186749] [INFO]   Shellcode size: 392 bytes
[1750767357.260760] [INFO]   MEMORY PROTECTION for Shellcode memory at address 0x1273a670000:
[1750767357.261280] [INFO]     Base address: 0x1273a670000
[1750767357.261490] [INFO]     Allocation base: 0x1273a670000
[1750767357.261730] [INFO]     Allocation protection: 0x40
[1750767357.261941] [INFO]     Region size: 4096 bytes
[1750767357.262122] [INFO]     Current protection: 0x40
[1750767357.262418] [INFO]     Memory state: 0x1000
[1750767357.262521] [INFO]     Memory type: 0x20000
[1750767357.262663] [INFO]     Memory protection flags breakdown:
[1750767357.262955] [INFO]       PAGE_NOACCESS (0x01): Not set
[1750767357.263117] [INFO]       PAGE_READONLY (0x02): Not set
[1750767357.263318] [INFO]       PAGE_READWRITE (0x04): Not set
[1750767357.263531] [INFO]       PAGE_WRITECOPY (0x08): Not set
[1750767357.263738] [INFO]       PAGE_EXECUTE (0x10): Not set
[1750767357.263950] [INFO]       PAGE_EXECUTE_READ (0x20): Not set
[1750767357.264165] [INFO]       PAGE_EXECUTE_READWRITE (0x40): Set
[1750767357.264266] [INFO]       PAGE_EXECUTE_WRITECOPY (0x80): Not set
[1750767357.264406] [INFO]       PAGE_GUARD (0x100): Not set
[1750767357.264596] [INFO]       PAGE_NOCACHE (0x200): Not set
[1750767357.264803] [INFO]       PAGE_WRITECOMBINE (0x400): Not set
[1750767357.265027] [INFO]   First 64 bytes of shellcode:
[1750767357.265293] [INFO]     0000: 48 31 C9 48 81 E9 D4 FF FF FF 48 8D 05 EF FF FF  | H1.H......H.....
[1750767357.265520] [INFO]     0010: FF 48 BB 44 F6 A4 0B 5F 89 5D 7F 48 31 58 27 48  | .H.D..._.].H1X'H
[1750767357.265937] [INFO]     0020: 2D F8 FF FF FF E2 F4 B8 BE 27 EF AF 61 9D 7F 44  | -........'..a..D
[1750767357.266090] [INFO]     0030: F6 E5 5A 1E D9 0F 2E 12 BE 95 D9 3A C1 D6 2D 24  | ..Z........:..-$
[1750767357.266423] [INFO]   ==================== END SHELLCODE MEMORY DETAILS ====================
[1750767357.266817] [INFO]   Starting infinite fiber switching loop
[1750767357.266970] [INFO]   Entering infinite while(true) loop for fiber switching
[1750767357.267219] [INFO]   Starting infinite fiber switching loop for 100% fidelity with original implementation
[1750767357.267446] [INFO]   ==================== FIBER SWITCH START ====================
[1750767357.267766] [INFO]   Switching to shellcode fiber
[1750767357.348123] [INFO]   Preparing to switch to shellcode fiber
[1750767357.434007] [INFO]   MEMORY PROTECTION for Shellcode memory before fiber switch at address 0x1273a670000:
[1750767357.434413] [INFO]     Base address: 0x1273a670000
[1750767357.434613] [INFO]     Allocation base: 0x1273a670000
[1750767357.434769] [INFO]     Allocation protection: 0x40
[1750767357.434957] [INFO]     Region size: 4096 bytes
[1750767357.435186] [INFO]     Current protection: 0x40
[1750767357.435452] [INFO]     Memory state: 0x1000
[1750767357.435924] [INFO]     Memory type: 0x20000
[1750767357.436193] [INFO]     Memory protection flags breakdown:
[1750767357.436391] [INFO]       PAGE_NOACCESS (0x01): Not set
[1750767357.436612] [INFO]       PAGE_READONLY (0x02): Not set
[1750767357.436828] [INFO]       PAGE_READWRITE (0x04): Not set
[1750767357.437040] [INFO]       PAGE_WRITECOPY (0x08): Not set
[1750767357.437282] [INFO]       PAGE_EXECUTE (0x10): Not set
[1750767357.437539] [INFO]       PAGE_EXECUTE_READ (0x20): Not set
[1750767357.437716] [INFO]       PAGE_EXECUTE_READWRITE (0x40): Set
[1750767357.437957] [INFO]       PAGE_EXECUTE_WRITECOPY (0x80): Not set
[1750767357.438179] [INFO]       PAGE_GUARD (0x100): Not set
[1750767357.438375] [INFO]       PAGE_NOCACHE (0x200): Not set
[1750767357.438589] [INFO]       PAGE_WRITECOMBINE (0x400): Not set
[1750767357.438790] [INFO]   START OPERATION: SwitchToFiber (timestamp: 1750767357.438789)
[1750767357.439072] [INFO]   MEMORY ADDRESS: MAIN_FIBER before switch at 0x1273a69f400 (decimal: 1267995374592)
[1750767357.439262] [INFO]   MEMORY ADDRESS: SHELLCODE_FIBER before switch at 0x1273a69cfe0 (decimal: 1267995365344)
[1750767357.439422] [INFO]   FUNCTION CALL: SwitchToFiber(Function: 0x7ff6d0c889ac, Args: 1, Gadget: 0x7ffa403c2273, Fiber: 0x1273a69cfe0)
[1750767357.439656] [INFO]   Using CallR12 to call SwitchToFiber for 100% fidelity
[1750767357.439898] [INFO]   CRITICAL POINT: About to switch to shellcode fiber. If an access violation occurs, it will likely happen during this call.
