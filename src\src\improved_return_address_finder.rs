// 优化后的返回地址查找器
// 核心优化:
// 1. 缓存系统调用信息 (SSN/Syscall) 和 ROP Gadgets，避免重复查找。
// 2. 将重复的 NtQueryVirtualMemory 调用逻辑（包括重试）抽象为独立的辅助函数。
// 3. 将多次 RtlLookupFunctionEntry 调用合并为一次，并用`FunctionInfo`结构体缓存结果。
// 4. 增强地址验证，通过PE节头检查代码段属性，确保地址在可执行内存区。
// 5. 严格遵循所有系统调用均通过 CallMe 和 CallR12 执行的约束。

use std::collections::HashMap;
use std::ffi::c_void;
use std::mem::size_of;
use std::ptr::null_mut;
use std::sync::Mutex;

use lazy_static::lazy_static;

use windows_sys::Win32::System::Memory::{
    MEMORY_BASIC_INFORMATION, PAGE_EXECUTE, PAGE_EXECUTE_READ, PAGE_EXECUTE_READWRITE,
    PAGE_EXECUTE_WRITECOPY,
};
use windows_sys::Win32::System::SystemServices::{
    IMAGE_DOS_HEADER,
};

use crate::hde64::{hde64_disasm, HDE64};
use crate::{
    get_function_address, go_go_gadget, nt_current_process, ssn_lookup, CallMe, CallR12,
    SyscallEntry, get_call_r12_gadgets, DW_SSN, get_ntdll, QW_JMP, RUNTIME_FUNCTION,
    IMAGE_NT_HEADERS64, IMAGE_OPTIONAL_HEADER64, IMAGE_SECTION_HEADER, IMAGE_SCN_MEM_EXECUTE,
    UNWIND_INFO, with_logger,
};

// 使用项目的全局 logger 系统进行日志输出
macro_rules! dbg_log {
    ($($arg:tt)*) => {
        unsafe {
            with_logger(|logger| {
                logger.debug(&format!("[RETURN_ADDR_FINDER] {}", format!($($arg)*)));
            });
        }
    };
}

macro_rules! info_log {
    ($($arg:tt)*) => {
        unsafe {
            with_logger(|logger| {
                logger.info(&format!("[RETURN_ADDR_FINDER] {}", format!($($arg)*)));
            });
        }
    };
}

macro_rules! error_log {
    ($($arg:tt)*) => {
        unsafe {
            with_logger(|logger| {
                logger.error(&format!("[RETURN_ADDR_FINDER] {}", format!($($arg)*)));
            });
        }
    };
}

// 将系统调用名称定义为常量
const ZW_QUERY_VIRTUAL_MEMORY: &str = "ZwQueryVirtualMemory";
const NT_QUERY_VIRTUAL_MEMORY: &str = "NtQueryVirtualMemory";
const RTL_LOOKUP_FUNCTION_ENTRY: &str = "RtlLookupFunctionEntry";

// Thread-safe syscall entry for caching
#[derive(Copy, Clone)]
struct CachedSyscallEntry {
    ssn: u32,
    address: usize,
    syscall: usize,
}

impl From<SyscallEntry> for CachedSyscallEntry {
    fn from(entry: SyscallEntry) -> Self {
        Self {
            ssn: entry.ssn,
            address: entry.address as usize,
            syscall: entry.syscall as usize,
        }
    }
}

impl From<CachedSyscallEntry> for SyscallEntry {
    fn from(cached: CachedSyscallEntry) -> Self {
        Self {
            ssn: cached.ssn,
            address: cached.address as *mut u8,
            syscall: cached.syscall as *mut c_void,
        }
    }
}

// --- 缓存结构 ---
lazy_static! {
    static ref SYSCALL_CACHE: Mutex<HashMap<&'static str, CachedSyscallEntry>> = Mutex::new(HashMap::new());
    // Fix: Store the gadget as a usize to make it Sync (thread-safe for lazy_static)
    static ref GADGET_CACHE: Option<usize> = unsafe {
        let gadgets = get_call_r12_gadgets();
        if !gadgets.is_empty() {
            let gadget = go_go_gadget(gadgets);
            if !gadget.is_null() {
                info_log!("成功缓存 ROP gadget at {:p}", gadget);
                return Some(gadget as usize);
            }
        }
        error_log!("警告: 未找到并缓存 ROP gadget");
        None
    };
}

// --- 数据结构 ---

/// 存储从RtlLookupFunctionEntry获取的函数信息，避免重复调用
struct FunctionInfo {
    image_base: *mut c_void,
    function_start: *mut c_void,
    function_end: *mut c_void,
    prologue_end: *mut c_void,
    total_size: usize,
}

// --- 核心辅助函数 ---

/// 从缓存或通过ssn_lookup获取系统调用信息
fn get_syscall(name: &'static str) -> Option<SyscallEntry> {
    let mut cache = SYSCALL_CACHE.lock().unwrap();
    if let Some(cached_syscall) = cache.get(name) {
        dbg_log!("从缓存中获取系统调用 {}", name);
        return Some((*cached_syscall).into());
    }
    // Fix: Add unsafe block for unsafe function call
    let syscall = unsafe { ssn_lookup(name) };
    if syscall.ssn != 0 {
        cache.insert(name, syscall.into());
        info_log!("成功查找并缓存系统调用 {} (SSN: {})", name, syscall.ssn);
        Some(syscall)
    } else {
        error_log!("无法找到系统调用: {}", name);
        None
    }
}

/// **[重构核心]** 使用 CallR12 安全地调用 NtQueryVirtualMemory
unsafe fn query_virtual_memory_with_retry(
    address: *mut c_void,
) -> Option<MEMORY_BASIC_INFORMATION> {
    // Fix: Cast the cached usize back to a pointer
    let Some(gadget_addr) = *GADGET_CACHE else { 
        error_log!("无法获取缓存的 ROP gadget");
        return None; 
    };
    let gadget = gadget_addr as *mut c_void;

    // Fix: Initialize the struct with zeros as it doesn't implement Default
    let mut mbi: MEMORY_BASIC_INFORMATION = std::mem::zeroed();
    let mut return_length: usize = 0;

    dbg_log!("开始查询虚拟内存地址: {:p}", address);

    for &syscall_name in &[ZW_QUERY_VIRTUAL_MEMORY, NT_QUERY_VIRTUAL_MEMORY] {
        if let Some(syscall) = get_syscall(syscall_name) {
            DW_SSN = syscall.ssn;
            QW_JMP = syscall.syscall;
            
            dbg_log!("尝试使用系统调用 {} (SSN: {}) 查询地址 {:p}", syscall_name, syscall.ssn, address);
            
            let result = CallR12(
                CallMe as *mut c_void,
                6,
                gadget,
                nt_current_process(),
                address,
                0 as *mut c_void,
                &mut mbi as *mut _ as *mut c_void,
                size_of::<MEMORY_BASIC_INFORMATION>() as *mut c_void,
                &mut return_length as *mut _ as *mut c_void,
            ) as usize;
            
            if result == 0 {
                info_log!("NtQueryVirtualMemory 成功 (使用 {}): 地址={:p}, 保护={:#x}, 大小={:#x}", 
                    syscall_name, address, mbi.Protect, mbi.RegionSize);
                return Some(mbi);
            } else {
                dbg_log!("NtQueryVirtualMemory 失败 (使用 {}): 地址={:p}, 错误码={:#x}", 
                    syscall_name, address, result);
            }
        }
    }
    
    error_log!("所有 NtQueryVirtualMemory 尝试均失败 for address {:p}", address);
    None
}

/// **[新增]** 一次性获取函数所有信息
unsafe fn get_function_info(function_address: *mut c_void) -> Option<FunctionInfo> {
    dbg_log!("开始获取函数 {:p} 的信息", function_address);
    
    let Some(gadget_addr) = *GADGET_CACHE else { 
        error_log!("无法获取缓存的 ROP gadget");
        return None; 
    };
    let gadget = gadget_addr as *mut c_void;

    let ntdll = get_ntdll();
    let rtl_lookup_addr = get_function_address(ntdll, RTL_LOOKUP_FUNCTION_ENTRY.as_bytes());
    if rtl_lookup_addr.is_null() {
        error_log!("无法获取 RtlLookupFunctionEntry 地址");
        return None;
    }
    
    dbg_log!("调用 RtlLookupFunctionEntry for function {:p}", function_address);

    let mut image_base: u64 = 0;
    let runtime_function = CallR12(
        rtl_lookup_addr,
        3,
        gadget,
        function_address as u64 as *mut c_void,
        &mut image_base as *mut u64 as *mut c_void,
        null_mut::<c_void>(),
    ) as *const RUNTIME_FUNCTION;

    if runtime_function.is_null() || image_base == 0 {
        error_log!("RtlLookupFunctionEntry failed for {:p}", function_address);
        return None;
    }

    let image_base_ptr = image_base as *mut c_void;
    let unwind_info_rva = (*runtime_function).UnwindInfoAddress;
    let unwind_info = (image_base + unwind_info_rva as u64) as *const UNWIND_INFO;
    let size_of_prolog = (*unwind_info).SizeOfProlog;
    let begin_address_rva = (*runtime_function).BeginAddress;
    let end_address_rva = (*runtime_function).EndAddress;

    let function_start = (image_base + begin_address_rva as u64) as *mut c_void;
    let function_end = (image_base + end_address_rva as u64) as *mut c_void;
    let prologue_end = function_start.add(size_of_prolog as usize);
    let total_size = function_end as usize - function_start as usize;

    info_log!(
        "函数信息: start={:p}, end={:p}, size={}, prologue_end={:p}",
        function_start,
        function_end,
        total_size,
        prologue_end
    );

    Some(FunctionInfo {
        image_base: image_base_ptr,
        function_start,
        function_end,
        prologue_end,
        total_size,
    })
}

/// **[增强]** 检查地址是否有效、位于模块内，并且在可执行节区中。
unsafe fn is_valid_executable_address(address: *mut c_void, module_base: *mut c_void) -> bool {
    if address.is_null() || module_base.is_null() {
        dbg_log!("地址验证失败: address={:p}, module_base={:p} (null pointer)", address, module_base);
        return false;
    }

    dbg_log!("验证地址可执行性: address={:p}, module_base={:p}", address, module_base);

    // 1. 快速检查内存页保护属性
    if let Some(mbi) = query_virtual_memory_with_retry(address) {
        let is_executable_page = matches!(
            mbi.Protect,
            PAGE_EXECUTE | PAGE_EXECUTE_READ | PAGE_EXECUTE_READWRITE | PAGE_EXECUTE_WRITECOPY
        );
        if !is_executable_page {
            error_log!(
                "地址 {:p} 内存保护属性 ({:#x}) 不可执行",
                address, mbi.Protect
            );
            return false;
        } else {
            dbg_log!("地址 {:p} 内存保护属性验证通过 ({:#x})", address, mbi.Protect);
        }
    } else {
        error_log!(
            "query_virtual_memory_with_retry 失败，无法验证 {:p}",
            address
        );
        return false; // 如果无法查询内存，则认为无效
    }

    // 2. 精确检查PE节头，确认地址在可执行节区内
    let dos_header = module_base as *const IMAGE_DOS_HEADER;
    if (*dos_header).e_magic != 0x5A4D {
        return false;
    } // "MZ"

    let nt_headers =
        (module_base as *const u8).add((*dos_header).e_lfanew as usize) as *const IMAGE_NT_HEADERS64;
    if (*nt_headers).Signature != 0x4550 {
        return false;
    } // "PE"

    let module_size = (*nt_headers).OptionalHeader.SizeOfImage as usize;
    let addr_offset = address as usize - module_base as usize;
    if addr_offset >= module_size {
        error_log!(
            "地址 {:p} 超出模块 {:p} 范围 (size: {:#x})",
            address, module_base, module_size
        );
        return false;
    }

    let sections = (&(*nt_headers).OptionalHeader as *const _ as *const u8)
        .add(size_of::<IMAGE_OPTIONAL_HEADER64>()) as *const IMAGE_SECTION_HEADER;
    let number_of_sections = (*nt_headers).FileHeader.NumberOfSections;

    for i in 0..number_of_sections {
        let section = &*sections.add(i as usize);
        let section_start = section.VirtualAddress as usize;
        let section_end = section_start + section.Misc.VirtualSize as usize;
        if addr_offset >= section_start && addr_offset < section_end {
            if (section.Characteristics & IMAGE_SCN_MEM_EXECUTE) != 0 {
                info_log!("地址 {:p} 位于可执行节区，验证通过", address);
                return true;
            } else {
                error_log!("地址 {:p} 位于不可执行节区", address);
                return false;
            }
        }
    }

    error_log!("地址 {:p} 未在任何节区中找到", address);
    false
}

/// 使用HDE64反汇编器查找返回指令
pub unsafe fn find_return_instruction(code: *const u8, max_length: usize) -> Option<*mut c_void> {
    if code.is_null() || max_length == 0 {
        return None;
    }
    let mut offset: usize = 0;
    let mut hs = HDE64::default();

    while offset < max_length {
        let current_code = code.add(offset);
        let len = hde64_disasm(current_code, &mut hs, max_length - offset);
        if len == 0 || len > 15 {
            break;
        }
        if matches!(hs.opcode, 0xC2 | 0xC3 | 0xCA | 0xCB) {
            info_log!("在偏移 {} 处找到返回指令 (0x{:02X})", offset, hs.opcode);
            return Some(current_code as *mut c_void);
        }
        offset += len as usize;
    }
    None
}

/// **[主函数]** 查找函数的返回地址
pub unsafe fn find_return_address(function_address: *mut c_void) -> *mut c_void {
    if function_address.is_null() {
        return null_mut();
    }
    info_log!("开始为函数 {:p} 查找返回地址", function_address);

    // 1. **[优化]** 一次性获取所有函数信息
    let Some(info) = get_function_info(function_address) else {
        // 如果无法获取 unwind info，回退到暴力扫描
        error_log!("无法获取函数信息，回退到暴力扫描");
        return find_return_instruction(function_address as *const u8, 5000).unwrap_or(null_mut());
    };

    // 2. 方法一：从函数序言之后开始精确扫描
    let scan_start_after_prologue = info.prologue_end as *const u8;
    let scan_len_after_prologue = info.function_end as usize - info.prologue_end as usize;
    if let Some(ret_addr) =
        find_return_instruction(scan_start_after_prologue, scan_len_after_prologue)
    {
        if is_valid_executable_address(ret_addr, info.image_base) {
            info_log!("方法一 (Unwind Info) 找到有效返回地址: {:p}", ret_addr);
            return ret_addr;
        }
    }

    // 3. 方法二：从函数头开始完整扫描
    if let Some(ret_addr) = find_return_instruction(info.function_start as *const u8, info.total_size)
    {
        if is_valid_executable_address(ret_addr, info.image_base) {
            info_log!("方法二 (完整扫描) 找到有效返回地址: {:p}", ret_addr);
            return ret_addr;
        }
    }

    error_log!(
        "所有智能方法均失败，未能找到 {:p} 的返回地址",
        function_address
    );
    null_mut()
}
